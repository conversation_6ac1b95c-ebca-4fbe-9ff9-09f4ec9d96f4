const bcrypt = require('bcryptjs');
const { query } = require('../models/database');
const { generateToken } = require('../middleware/auth');
const { validateEmail, validatePassword, sanitizeInput } = require('../utils/validation');

const register = async (req, res) => {
  try {
    const { email, password } = req.body;
    
    // Sanitize inputs
    const sanitizedEmail = sanitizeInput(email?.toLowerCase());
    
    // Validate inputs with Arabic messages
    if (!sanitizedEmail || !password) {
      return res.status(400).json({
        success: false,
        message: 'البريد الإلكتروني وكلمة المرور مطلوبان'
      });
    }

    if (!validateEmail(sanitizedEmail)) {
      return res.status(400).json({
        success: false,
        message: 'تنسيق البريد الإلكتروني غير صالح'
      });
    }

    if (!validatePassword(password)) {
      return res.status(400).json({
        success: false,
        message: 'يجب أن تكون كلمة المرور 8 أحرف على الأقل وتحتوي على حرف واحد ورقم واحد على الأقل'
      });
    }

    // Check if user already exists
    const existingUser = await query(
      'SELECT id FROM users WHERE email = $1',
      [sanitizedEmail]
    );

    if (existingUser.rows.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'البريد الإلكتروني مسجل بالفعل'
      });
    }

    // Hash password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);
    
    // Create user with Arabic defaults
    const result = await query(
      'INSERT INTO users (email, password_hash, language, text_direction, role) VALUES ($1, $2, $3, $4, $5) RETURNING id, email, created_at, role',
      [sanitizedEmail, hashedPassword, 'ar', 'rtl', 'user']
    );

    const user = result.rows[0];

    // Generate JWT token
    const token = generateToken(user.id);

    // Log the registration
    await query(
      'INSERT INTO logs (user_id, action, details) VALUES ($1, $2, $3)',
      [user.id, 'USER_REGISTERED', { email: sanitizedEmail }]
    );

    res.status(201).json({
      success: true,
      message: 'تم إنشاء الحساب بنجاح',
      token,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        createdAt: user.created_at,
        language: 'ar',
        textDirection: 'rtl'
      }
    });
  } catch (error) {
    console.error('خطأ في التسجيل:', error);

    if (error.code === '23505') { // Unique constraint violation
      return res.status(400).json({
        success: false,
        message: 'البريد الإلكتروني مسجل بالفعل'
      });
    }

    res.status(500).json({
      success: false,
      message: 'فشل في إنشاء الحساب'
    });
  }
};

const login = async (req, res) => {
  try {
    const { email, password } = req.body;
    
    // Sanitize inputs
    const sanitizedEmail = sanitizeInput(email?.toLowerCase());
    
    // Validate inputs with Arabic messages
    if (!sanitizedEmail || !password) {
      return res.status(400).json({
        success: false,
        message: 'البريد الإلكتروني وكلمة المرور مطلوبان'
      });
    }

    if (!validateEmail(sanitizedEmail)) {
      return res.status(400).json({
        success: false,
        message: 'تنسيق البريد الإلكتروني غير صالح'
      });
    }

    // Find user
    const userResult = await query(
      'SELECT id, email, password_hash, role, created_at FROM users WHERE email = $1',
      [sanitizedEmail]
    );

    if (userResult.rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
      });
    }

    const user = userResult.rows[0];

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    
    if (!isValidPassword) {
      // Log failed login attempt
      await query(
        'INSERT INTO logs (user_id, action, details) VALUES ($1, $2, $3)',
        [user.id, 'LOGIN_FAILED', { email: sanitizedEmail, reason: 'invalid_password' }]
      );
      
      return res.status(401).json({
        success: false,
        message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
      });
    }

    // Generate JWT token
    const token = generateToken(user.id);

    // Log successful login
    await query(
      'INSERT INTO logs (user_id, action, details) VALUES ($1, $2, $3)',
      [user.id, 'LOGIN_SUCCESS', { email: sanitizedEmail }]
    );

    // Update user to Arabic if not already set
    await query(
      'UPDATE users SET language = $1, text_direction = $2 WHERE id = $3 AND (language IS NULL OR language != $1)',
      ['ar', 'rtl', user.id]
    );

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      token,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        createdAt: user.created_at,
        language: 'ar',
        textDirection: 'rtl'
      }
    });
  } catch (error) {
    console.error('خطأ في تسجيل الدخول:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في تسجيل الدخول'
    });
  }
};

const getProfile = async (req, res) => {
  try {
    const { userId } = req.user;

    const userResult = await query(
      `SELECT id, email, full_name, phone_number, language, text_direction,
              whatsapp_notifications_enabled, notification_preferences, created_at, role
       FROM users WHERE id = $1`,
      [userId]
    );

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    const user = userResult.rows[0];

    res.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        fullName: user.full_name,
        phoneNumber: user.phone_number,
        language: user.language || 'ar',
        textDirection: user.text_direction || 'rtl',
        whatsappNotificationsEnabled: user.whatsapp_notifications_enabled,
        notificationPreferences: user.notification_preferences || {
          document_signed: true,
          document_uploaded: false,
          admin_notifications: true
        },
        createdAt: user.created_at
      }
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في تحميل الملف الشخصي'
    });
  }
};

const updateProfile = async (req, res) => {
  try {
    const { userId } = req.user;
    const {
      fullName,
      phoneNumber,
      whatsappNotificationsEnabled,
      notificationPreferences
    } = req.body;

    // Validate phone number if provided
    if (phoneNumber) {
      const { validatePhoneNumber } = require('../services/whatsappNotificationService');
      if (!validatePhoneNumber(phoneNumber)) {
        return res.status(400).json({
          success: false,
          message: 'تنسيق رقم الهاتف غير صالح. يجب أن يكون بالتنسيق الدولي (+966501234567)'
        });
      }
    }

    // Build update query dynamically
    const updates = [];
    const values = [];
    let paramCount = 0;

    if (fullName !== undefined) {
      paramCount++;
      updates.push(`full_name = $${paramCount}`);
      values.push(fullName?.trim() || null);
    }

    if (phoneNumber !== undefined) {
      paramCount++;
      updates.push(`phone_number = $${paramCount}`);
      values.push(phoneNumber?.trim() || null);
    }

    if (whatsappNotificationsEnabled !== undefined) {
      paramCount++;
      updates.push(`whatsapp_notifications_enabled = $${paramCount}`);
      values.push(Boolean(whatsappNotificationsEnabled));
    }

    if (notificationPreferences !== undefined) {
      paramCount++;
      updates.push(`notification_preferences = $${paramCount}`);
      values.push(JSON.stringify(notificationPreferences));
    }

    if (updates.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'لا توجد بيانات للتحديث'
      });
    }

    // Add updated_at
    paramCount++;
    updates.push(`updated_at = $${paramCount}`);
    values.push(new Date());

    // Add user ID for WHERE clause
    paramCount++;
    values.push(userId);

    const updateQuery = `
      UPDATE users
      SET ${updates.join(', ')}
      WHERE id = $${paramCount}
      RETURNING id, email, full_name, phone_number, whatsapp_notifications_enabled,
                notification_preferences, updated_at
    `;

    const result = await query(updateQuery, values);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    const user = result.rows[0];

    // Log the profile update
    await query(
      'INSERT INTO logs (user_id, action, details) VALUES ($1, $2, $3)',
      [userId, 'PROFILE_UPDATED', {
        updatedFields: Object.keys(req.body),
        phoneNumberUpdated: phoneNumber !== undefined,
        notificationSettingsUpdated: whatsappNotificationsEnabled !== undefined || notificationPreferences !== undefined
      }]
    );

    res.json({
      success: true,
      message: 'تم تحديث الملف الشخصي بنجاح',
      user: {
        id: user.id,
        email: user.email,
        fullName: user.full_name,
        phoneNumber: user.phone_number,
        whatsappNotificationsEnabled: user.whatsapp_notifications_enabled,
        notificationPreferences: user.notification_preferences,
        updatedAt: user.updated_at
      }
    });

  } catch (error) {
    console.error('Update profile error:', error);

    if (error.code === '23505') { // Unique constraint violation
      return res.status(400).json({
        success: false,
        message: 'رقم الهاتف مستخدم بالفعل'
      });
    }

    res.status(500).json({
      success: false,
      message: 'فشل في تحديث الملف الشخصي'
    });
  }
};

// Get all users (admin function)
const getAllUsers = async (req, res) => {
  try {
    const result = await query(
      `SELECT
        id,
        email,
        full_name,
        created_at,
        updated_at,
        CASE
          WHEN updated_at > NOW() - INTERVAL '30 days' THEN 'active'
          ELSE 'inactive'
        END as status
      FROM users
      ORDER BY created_at DESC`
    );

    res.json({
      success: true,
      users: result.rows
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في تحميل قائمة المستخدمين'
    });
  }
};

const changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user.userId;

    // Validate input
    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: 'كلمة المرور الحالية والجديدة مطلوبتان'
      });
    }

    // Validate new password
    if (!validatePassword(newPassword)) {
      return res.status(400).json({
        success: false,
        message: 'يجب أن تكون كلمة المرور الجديدة 8 أحرف على الأقل وتحتوي على حرف واحد ورقم واحد على الأقل'
      });
    }

    // Get current user data
    const userResult = await query('SELECT password_hash FROM users WHERE id = $1', [userId]);
    if (userResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    const user = userResult.rows[0];

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password_hash);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: 'كلمة المرور الحالية غير صحيحة'
      });
    }

    // Check if new password is different from current
    const isSamePassword = await bcrypt.compare(newPassword, user.password_hash);
    if (isSamePassword) {
      return res.status(400).json({
        success: false,
        message: 'كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية'
      });
    }

    // Hash new password
    const saltRounds = 12;
    const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

    // Update password in database
    await query(
      'UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
      [hashedNewPassword, userId]
    );

    res.json({
      success: true,
      message: 'تم تغيير كلمة المرور بنجاح'
    });

  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم أثناء تغيير كلمة المرور'
    });
  }
};

module.exports = {
  register,
  login,
  getProfile,
  updateProfile,
  getAllUsers,
  changePassword
};
