import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './services/AuthContext';
import { LanguageProvider } from './contexts/LanguageContext';
import ProtectedRoute from './components/ProtectedRoute';
import AdminRoute from './components/AdminRoute';
import Navbar from './components/Navbar';
import SessionTimeoutWarning from './components/SessionTimeoutWarning';
import Breadcrumb from './components/Breadcrumb';
import GlobalErrorBoundary from './components/GlobalErrorBoundary';
import NetworkStatus, { NetworkToast } from './components/NetworkStatus';
import Login from './pages/Login';
import Register from './pages/Register';
import Dashboard from './pages/Dashboard';
import SignatureUpload from './pages/SignatureUpload';
import DocumentSigning from './pages/DocumentSigning';
import SigningConfirmation from './pages/SigningConfirmation';
import SigningError from './pages/SigningError';
import History from './pages/History';
import Users from './pages/Users';
import UserSettings from './pages/UserSettings';
import SerialVerification from './pages/SerialVerification';
import Mail from './pages/Mail';
import AdminDocumentSigning from './pages/AdminDocumentSigning';
import AdminRecords from './pages/AdminRecords';
import PDFViewerTest from './components/PDFViewerTest';
import SystemDocumentation from './pages/SystemDocumentation';

function App() {
  useEffect(() => {
    // Set document direction to RTL for Arabic
    document.documentElement.dir = 'rtl';
    document.documentElement.lang = 'ar';

    // Load Almarai font from Google Fonts
    const link = document.createElement('link');
    link.href = 'https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap';
    link.rel = 'stylesheet';
    document.head.appendChild(link);
  }, []);

  return (
    <GlobalErrorBoundary>
      <LanguageProvider>
        <AuthProvider>
          <Router
            future={{
              v7_startTransition: true,
              v7_relativeSplatPath: true
            }}
          >
            <div className="min-h-screen bg-gray-50" dir="rtl">
              <Navbar />
              <SessionTimeoutWarning />
              <NetworkStatus />
              <NetworkToast />
              <Breadcrumb />
              <main className="container mx-auto px-4 py-8">
              <Routes>
                <Route path="/login" element={<Login />} />
                <Route path="/register" element={<Register />} />
                <Route path="/" element={<Navigate to="/dashboard" replace />} />
                <Route
                  path="/dashboard"
                  element={
                    <ProtectedRoute>
                      <Dashboard />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/signature-upload"
                  element={
                    <ProtectedRoute>
                      <SignatureUpload />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/document-signing"
                  element={
                    <AdminRoute requiredPermission="sign_documents">
                      <DocumentSigning />
                    </AdminRoute>
                  }
                />
                <Route
                  path="/sign"
                  element={
                    <AdminRoute requiredPermission="sign_documents">
                      <DocumentSigning />
                    </AdminRoute>
                  }
                />
                <Route
                  path="/signing-confirmation/:documentId"
                  element={
                    <ProtectedRoute>
                      <SigningConfirmation />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/signing-error"
                  element={
                    <ProtectedRoute>
                      <SigningError />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/history"
                  element={
                    <AdminRoute requiredPermission="view_history" requireAdminRole={true}>
                      <History />
                    </AdminRoute>
                  }
                />
                <Route
                  path="/documents"
                  element={
                    <ProtectedRoute>
                      <History />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/mail"
                  element={
                    <ProtectedRoute>
                      <Mail />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/admin/document-signing"
                  element={
                    <AdminRoute requiredPermission="sign_documents">
                      <AdminDocumentSigning />
                    </AdminRoute>
                  }
                />
                <Route
                  path="/admin/records"
                  element={
                    <AdminRoute requireAdminRole={true} requiredPermission="view_history">
                      <AdminRecords />
                    </AdminRoute>
                  }
                />
                <Route
                  path="/users"
                  element={
                    <AdminRoute requiredPermission="manage_users" requireAdminRole={true}>
                      <Users />
                    </AdminRoute>
                  }
                />
                <Route
                  path="/settings"
                  element={
                    <ProtectedRoute>
                      <UserSettings />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/verify"
                  element={
                    <SerialVerification />
                  }
                />
                <Route
                  path="/pdf-test"
                  element={
                    <ProtectedRoute>
                      <PDFViewerTest />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/documentation"
                  element={
                    <AdminRoute requiredPermission="view_dashboard" requireAdminRole={true}>
                      <SystemDocumentation />
                    </AdminRoute>
                  }
                />
              </Routes>
            </main>
          </div>
        </Router>
      </AuthProvider>
    </LanguageProvider>
    </GlobalErrorBoundary>
  );
}

export default App;
